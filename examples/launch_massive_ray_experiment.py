#!/usr/bin/env python
"""
Mega Ray Tune sweep
===================

Runs *all* (dataset, model) pairs **concurrently** inside ONE Ray Tune
experiment, so both GPUs and all CPUs stay busy.

Key features
------------
▪ Uses the ``train_one_model`` function already defined in *model_train_ray_o3.py*  
▪ Puts ``dataset_cfg_path`` and ``model_cfg_path`` into the Ray
  search space via ``tune.grid_search(...)`` – every combination is visible to
  <PERSON> at once  
▪ Supports Optuna + ASHA, resume="AUTO", WandB logging, etc.  
▪ Leaves two CPU cores free for the Ray head; each trial gets the rest
  (default 8 CPUs + 0.25 GPU)  

Example
--------
```bash
python mega_ray_sweep.py \
    --dataset-dir /mnt/ssd/YatesMarmoV1/conv_model_fits/data_configs/multi_dataset_temporal_basis \
    --model-dir   /home/<USER>/repos/DataYatesV1/examples/configs/ray_experiment \
    --trials-per-combo 4 \
    --max-epochs 30 \
    --cpus-per-trial 8 \
    --gpus-per-trial 0.25 \
    --max-concurrent 16
"""

from future import annotations

import argparse
import os
import time
from pathlib import Path
from typing import List

import ray
from ray import tune
from ray.tune.schedulers import ASHAScheduler
from ray.tune.search.optuna import OptunaSearch
import optuna

---------------------------------------------------------------------
Import the trainable & helper ripped from your existing script
---------------------------------------------------------------------
from model_train_ray_o3 import train_one_model, build_search # type: ignore

---------------------------------------------------------------------
CLI
---------------------------------------------------------------------
def get_args():
p = argparse.ArgumentParser("Mega Ray sweep")
p.add_argument("--dataset-dir", required=True, type=Path,
help="Directory containing *.yaml dataset configs")
p.add_argument("--model-dir", required=True, type=Path,
help="Directory containing *.yaml model configs")
p.add_argument("--trials-per-combo", type=int, default=4,
help="# hyperparameter samples PER (dataset, model)")
p.add_argument("--max-epochs", type=int, default=30)
p.add_argument("--cpus-per-trial", type=int, default=8)
p.add_argument("--gpus-per-trial", type=float, default=0.25)
p.add_argument("--max-concurrent", type=int, default=16,
help="Max concurrently running trials")
p.add_argument("--resume", action="store_true",
help="Resume an interrupted mega run")
p.add_argument("--dry-run", action="store_true",
help="List search space but do not run")
return p.parse_args()

---------------------------------------------------------------------
Main
---------------------------------------------------------------------
def main():
args = get_args()
# -----------------------------------------------------------------
#  Collect dataset & model YAMLs
# -----------------------------------------------------------------
datasets: List[Path] = sorted(args.dataset_dir.glob("*.yaml"))
models:   List[Path] = sorted(args.model_dir.glob("*.yaml"))

if not datasets or not models:
    raise RuntimeError("No dataset or model YAMLs found!")

print(f"📊  {len(datasets)} datasets × {len(models)} models"
      f" × {args.trials_per_combo} trials ="
      f" {len(datasets)*len(models)*args.trials_per_combo:,} total trials")

if args.dry_run:
    print("🧪  Dry-run finished.")
    return 0

# -----------------------------------------------------------------
#  Ray init (leave 2 cores for the head / OS)
# -----------------------------------------------------------------
os.environ["OMP_NUM_THREADS"] = "1"
ray.init(num_cpus=os.cpu_count() - 2,
         num_gpus=ray.util.get_gpu_ids().__len__(),
         include_dashboard=False)

# -----------------------------------------------------------------
#  Search + scheduler
# -----------------------------------------------------------------
algo = OptunaSearch(metric="val_loss", mode="min",
                    sampler=optuna.samplers.TPESampler(seed=42))

scheduler = ASHAScheduler(max_t=args.max_epochs,
                          grace_period=max(1, args.max_epochs // 10),
                          reduction_factor=3,
                          time_attr="epoch",
                          metric="val_loss",
                          mode="min")

# -----------------------------------------------------------------
#  Build search space
# -----------------------------------------------------------------
config = {
    # large discrete grid
    "dataset_cfg_path": tune.grid_search(datasets),
    "model_cfg_path":   tune.grid_search(models),
    # continuous / categorical hyper-params
    **build_search(),
}

# -----------------------------------------------------------------
#  Wrap the trainable with static kwargs
# -----------------------------------------------------------------
trainable = tune.with_parameters(
    train_one_model,
    max_epochs=args.max_epochs,
    gpus_per_trial=args.gpus_per_trial,
    cpus_per_trial=args.cpus_per_trial,
    enable_wandb=True,
)

# -----------------------------------------------------------------
#  Run!
# -----------------------------------------------------------------
experiment_name = f"mega_sweep_{int(time.time())}"
analysis = tune.run(
    trainable,
    name=experiment_name,
    config=config,
    num_samples=args.trials_per_combo,
    scheduler=scheduler,
    search_alg=algo,
    resources_per_trial={"cpu": args.cpus_per_trial,
                         "gpu": args.gpus_per_trial},
    max_concurrent_trials=args.max_concurrent,
    resume="AUTO" if args.resume else False,
    raise_on_failed_trial=False,
    fail_fast=False,
    log_to_file=True,
)

best_overall = analysis.best_result["val_loss"]
print(f"✅ Mega sweep finished — best val_loss = {best_overall:.4f}")
print(f"🗂  Results: ~/ray_results/{experiment_name}")
