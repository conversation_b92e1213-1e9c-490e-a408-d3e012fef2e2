# Massive Ray Experiment - Quick Commands

# This is what I launched
python model_train_ray_o3.py \
    --dataset-dir /mnt/ssd/YatesMarmoV1/conv_model_fits/data_configs/multi_dataset_temporal_basis \
    --model-configs /home/<USER>/repos/DataYatesV1/examples/configs/ray_experiment/*.yaml \
    --batch \
    --trials 4 \
    --max-epochs 30 \
    --max-concurrent 7 \
    --cpus-per-trial 8 \
    --gpus-per-trial 0.25 \
    2>&1 | tee massive_experiment.log

## Current Status
- ✅ O3 memory approach tested and working
- 🔄 Wandb logging test (next step)
- 🔄 Ready for massive launch

## Next Steps

### 1. Wandb Test (after current test finishes)
```bash
cd /home/<USER>/repos/DataYatesV1/examples

# Edit test_ray_minimal.py:
# Change: 'enable_wandb': True
# Change: 'num_trials': 2
# Change: 'max_epochs_per_trial': 1

python test_ray_minimal.py
```

### 2. Launch Massive Experiment

#### Option A: Full Massive (120 combinations)
```bash
cd /home/<USER>/repos/DataYatesV1/examples

# Full experiment - will take ~2-3 days
python launch_massive_ray_experiment.py \
    --trials-per-combo 25 \
    --max-epochs 20 \
    --max-concurrent 4

# Or use model_train_ray_o3.py directly for single combinations
python model_train_ray_o3.py \
    --dataset-dir /mnt/ssd/YatesMarmoV1/conv_model_fits/data_configs/multi_dataset_temporal_basis \
    --model-configs /home/<USER>/repos/DataYatesV1/examples/configs/ray_experiment/*.yaml \
    --batch
```

#### Option B: Subset Test (10 combinations)
```bash
# Test with subset first
python launch_massive_ray_experiment.py \
    --trials-per-combo 10 \
    --max-epochs 10 \
    --subset 10 \
    --max-concurrent 4
```

#### Option C: Dry Run (see what would happen)
```bash
# See all commands without executing
python launch_massive_ray_experiment.py --dry-run
```

## Monitoring

### Check Ray Status
```bash
# Check running processes
ps aux | grep ray

# Check GPU usage
nvidia-smi

# Check Ray results
ls -la ~/ray_results/
```

### Check Wandb
- Dashboard: https://wandb.ai/yateslab/digital-twin-ray-tuning
- Filter by dataset: Use "group" filter
- Filter by model: Use "tags" filter

## Resource Usage Expectations
- **RAM**: ~200-300GB (out of 504GB available)
- **GPU**: Both GPUs utilized
- **CPU**: ~16-32 cores active
- **Storage**: ~10-50GB for results
- **Runtime**: 2-3 days for full experiment

## Emergency Commands

### Stop All Ray Processes
```bash
# Graceful stop
pkill -f ray

# Force stop if needed
pkill -9 -f ray
```

### Clean Up Ray Results
```bash
# Remove all Ray results (careful!)
rm -rf ~/ray_results/ray_*

# Remove specific experiment
rm -rf ~/ray_results/ray_DATASET_MODEL_*
```

## Configuration Files

### Key Files Modified:
- `examples/model_train_ray_o3.py` - Main O3 script with wandb
- `examples/test_ray_minimal.py` - Test script with wandb
- `examples/launch_massive_ray_experiment.py` - Batch launcher

### Key Paths:
- **Datasets**: `/mnt/ssd/YatesMarmoV1/conv_model_fits/data_configs/multi_dataset_temporal_basis/`
- **Models**: `/home/<USER>/repos/DataYatesV1/examples/configs/ray_experiment/`
- **Results**: `~/ray_results/`

## Troubleshooting

### If Memory Issues Return:
- Reduce `--max-concurrent` to 2
- Reduce `--cpus-per-trial` to 4
- Check for memory leaks with `htop`

### If Wandb Issues:
- Check wandb login: `wandb login`
- Check project exists: `wandb project list`
- Disable wandb temporarily: set `enable_wandb=False`

### If Ray Issues:
- Restart Ray: `ray stop && ray start --head`
- Check Ray dashboard: `ray dashboard`
- Check Ray logs: `ray logs`
