#!/usr/bin/env python
"""
Launch massive Ray hyperparameter tuning experiment

This script launches the full experiment across all dataset/model combinations:
- 30 datasets × 4 models = 120 combinations
- Configurable trials per combination
- Full wandb logging enabled
- Memory-efficient O3 approach

Usage:
    python launch_massive_ray_experiment.py --trials-per-combo 25 --max-epochs 20
"""

import os
import sys
import time
import argparse
from pathlib import Path
import subprocess
from typing import List

# Configuration for massive experiment
MASSIVE_CONFIG = {
    'dataset_configs_dir': '/mnt/ssd/YatesMarmoV1/conv_model_fits/data_configs/multi_dataset_temporal_basis',
    'model_configs_dir': '/home/<USER>/repos/DataYatesV1/examples/configs/ray_experiment',
    'trials_per_combo': 25,        # Trials per dataset/model combination
    'max_epochs': 20,              # Max epochs per trial
    'gpus_per_trial': 1,           # GPUs per trial
    'cpus_per_trial': 8,           # CPUs per trial  
    'max_concurrent': 4,           # Max concurrent trials
}

def get_all_combinations():
    """Get all dataset/model combinations for the massive experiment."""
    dataset_dir = Path(MASSIVE_CONFIG['dataset_configs_dir'])
    model_dir = Path(MASSIVE_CONFIG['model_configs_dir'])
    
    # Get dataset configs (exclude base config)
    dataset_configs = [f for f in dataset_dir.glob("*.yaml") if "base" not in f.name.lower()]
    
    # Get model configs
    model_configs = list(model_dir.glob("*.yaml"))
    
    if not dataset_configs:
        raise ValueError(f"No dataset configs found in {dataset_dir}")
    if not model_configs:
        raise ValueError(f"No model configs found in {model_dir}")
    
    # Create all combinations
    combinations = []
    for dataset_config in dataset_configs:
        for model_config in model_configs:
            combinations.append((dataset_config, model_config))
    
    return combinations, len(dataset_configs), len(model_configs)

def estimate_runtime(num_combinations, trials_per_combo, max_epochs):
    """Estimate total runtime for the experiment."""
    # Rough estimates based on test results
    minutes_per_epoch = 4  # From test observations
    minutes_per_trial = max_epochs * minutes_per_epoch
    total_trials = num_combinations * trials_per_combo
    
    # With parallelization
    concurrent_trials = MASSIVE_CONFIG['max_concurrent']
    total_minutes = (total_trials * minutes_per_trial) / concurrent_trials
    
    hours = total_minutes / 60
    days = hours / 24
    
    return total_trials, hours, days

def launch_experiment(dataset_config: Path, model_config: Path, args):
    """Launch Ray experiment for one dataset/model combination."""

    cmd = [
        "python", "model_train_ray_o3.py",
        "--dataset-config", str(dataset_config),
        "--model-configs", str(model_config),
        "--trials", str(args.trials_per_combo),
        "--max-epochs", str(args.max_epochs),
        "--gpus-per-trial", str(args.gpus_per_trial),
        "--cpus-per-trial", str(args.cpus_per_trial),
        "--max-concurrent", str(args.max_concurrent),
    ]
    
    print(f"🚀 Launching: {dataset_config.stem} × {model_config.stem}")
    print(f"   Command: {' '.join(cmd)}")
    
    if not args.dry_run:
        try:
            result = subprocess.run(cmd, check=True, capture_output=True, text=True)
            print(f"✅ Completed: {dataset_config.stem} × {model_config.stem}")
            return True
        except subprocess.CalledProcessError as e:
            print(f"❌ Failed: {dataset_config.stem} × {model_config.stem}")
            print(f"   Error: {e}")
            return False
    else:
        print(f"   (Dry run - not executed)")
        return True

def main():
    parser = argparse.ArgumentParser(description='Launch massive Ray hyperparameter experiment')
    
    # Experiment parameters
    parser.add_argument('--trials-per-combo', type=int, default=MASSIVE_CONFIG['trials_per_combo'],
                        help=f'Trials per dataset/model combination (default: {MASSIVE_CONFIG["trials_per_combo"]})')
    parser.add_argument('--max-epochs', type=int, default=MASSIVE_CONFIG['max_epochs'],
                        help=f'Max epochs per trial (default: {MASSIVE_CONFIG["max_epochs"]})')
    
    # Resource parameters
    parser.add_argument('--gpus-per-trial', type=float, default=MASSIVE_CONFIG['gpus_per_trial'],
                        help=f'GPUs per trial (default: {MASSIVE_CONFIG["gpus_per_trial"]})')
    parser.add_argument('--cpus-per-trial', type=int, default=MASSIVE_CONFIG['cpus_per_trial'],
                        help=f'CPUs per trial (default: {MASSIVE_CONFIG["cpus_per_trial"]})')
    parser.add_argument('--max-concurrent', type=int, default=MASSIVE_CONFIG['max_concurrent'],
                        help=f'Max concurrent trials (default: {MASSIVE_CONFIG["max_concurrent"]})')
    
    # Execution options
    parser.add_argument('--dry-run', action='store_true',
                        help='Show what would be run without executing')
    parser.add_argument('--subset', type=int, default=None,
                        help='Run only first N combinations (for testing)')
    
    args = parser.parse_args()
    
    print("🚀 Massive Ray Hyperparameter Tuning Experiment")
    print("=" * 60)
    
    # Get all combinations
    combinations, num_datasets, num_models = get_all_combinations()
    
    print(f"📊 Experiment Scope:")
    print(f"   Datasets: {num_datasets}")
    print(f"   Models: {num_models}")
    print(f"   Total combinations: {len(combinations)}")
    print(f"   Trials per combination: {args.trials_per_combo}")
    print(f"   Max epochs per trial: {args.max_epochs}")
    
    # Estimate runtime
    total_trials, hours, days = estimate_runtime(len(combinations), args.trials_per_combo, args.max_epochs)
    print(f"\n⏱️  Estimated Runtime:")
    print(f"   Total trials: {total_trials:,}")
    print(f"   Estimated time: {hours:.1f} hours ({days:.1f} days)")
    print(f"   Concurrent trials: {args.max_concurrent}")
    
    # Apply subset if requested
    if args.subset:
        combinations = combinations[:args.subset]
        print(f"\n🔬 Running subset: first {len(combinations)} combinations")
    
    if args.dry_run:
        print(f"\n🧪 DRY RUN - showing commands that would be executed:")
    else:
        print(f"\n⚠️  This will launch {len(combinations)} experiments!")
        response = input("Continue? (y/N): ")
        if response.lower() != 'y':
            print("Experiment cancelled.")
            return 0
    
    # Launch experiments
    print(f"\n🚀 Launching experiments...")
    start_time = time.time()
    
    successful = 0
    failed = 0
    
    for i, (dataset_config, model_config) in enumerate(combinations, 1):
        print(f"\n[{i}/{len(combinations)}] ", end="")
        
        if launch_experiment(dataset_config, model_config, args):
            successful += 1
        else:
            failed += 1
    
    # Summary
    end_time = time.time()
    runtime_hours = (end_time - start_time) / 3600
    
    print(f"\n" + "=" * 60)
    print(f"🎯 MASSIVE EXPERIMENT SUMMARY")
    print(f"=" * 60)
    print(f"Total combinations: {len(combinations)}")
    print(f"Successful: {successful}")
    print(f"Failed: {failed}")
    print(f"Runtime: {runtime_hours:.1f} hours")
    
    if not args.dry_run:
        print(f"\n📊 Check results in:")
        print(f"   Ray Tune: ~/ray_results/")
        print(f"   Wandb: https://wandb.ai/yateslab/digital-twin-ray-tuning")
    
    return 0 if failed == 0 else 1

if __name__ == "__main__":
    sys.exit(main())
